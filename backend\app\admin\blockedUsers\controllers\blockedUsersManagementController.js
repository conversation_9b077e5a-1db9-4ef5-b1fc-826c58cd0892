const BlockedUser = require('../../../../db/models/blockedUserModel');
const User = require('../../../../db/models/userModel');

// Get all blocked users with pagination and filters
const getAllBlockedUsers = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      reason,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (page - 1) * limit;
    const query = {};

    // Apply filters
    if (reason) query.reason = reason;

    // Sort configuration
    const sortConfig = {};
    sortConfig[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Build aggregation pipeline for search
    let pipeline = [
      { $match: query }
    ];

    // Add search functionality
    if (search) {
      pipeline.unshift({
        $lookup: {
          from: 'users',
          localField: 'blocker',
          foreignField: '_id',
          as: 'blockerUser'
        }
      });
      pipeline.unshift({
        $lookup: {
          from: 'users',
          localField: 'blocked',
          foreignField: '_id',
          as: 'blockedUser'
        }
      });
      pipeline.push({
        $match: {
          $or: [
            { 'blockerUser.userName': { $regex: search, $options: 'i' } },
            { 'blockerUser.firstName': { $regex: search, $options: 'i' } },
            { 'blockerUser.lastName': { $regex: search, $options: 'i' } },
            { 'blockedUser.userName': { $regex: search, $options: 'i' } },
            { 'blockedUser.firstName': { $regex: search, $options: 'i' } },
            { 'blockedUser.lastName': { $regex: search, $options: 'i' } },
            { notes: { $regex: search, $options: 'i' } }
          ]
        }
      });
    }

    // Add sorting, skip, and limit
    pipeline.push(
      { $sort: sortConfig },
      { $skip: skip },
      { $limit: parseInt(limit) }
    );

    // Execute aggregation or regular query
    let blockedUsers;
    if (search) {
      blockedUsers = await BlockedUser.aggregate(pipeline);
      // Populate the results
      await BlockedUser.populate(blockedUsers, [
        { path: 'blocker', select: 'userName firstName lastName email profile' },
        { path: 'blocked', select: 'userName firstName lastName email profile' }
      ]);
    } else {
      blockedUsers = await BlockedUser.find(query)
        .populate('blocker', 'userName firstName lastName email profile')
        .populate('blocked', 'userName firstName lastName email profile')
        .sort(sortConfig)
        .skip(skip)
        .limit(parseInt(limit));
    }

    const total = await BlockedUser.countDocuments(query);

    // Get statistics
    const stats = await BlockedUser.aggregate([
      {
        $group: {
          _id: null,
          totalBlocked: { $sum: 1 },
          byReason: {
            $push: '$reason'
          }
        }
      }
    ]);

    const reasonStats = {};
    if (stats.length > 0) {
      stats[0].byReason.forEach(reason => {
        reasonStats[reason] = (reasonStats[reason] || 0) + 1;
      });
    }

    res.json({
      success: true,
      data: {
        blockedUsers,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / limit),
          totalItems: total,
          hasNextPage: page < Math.ceil(total / limit),
          hasPrevPage: page > 1
        },
        statistics: {
          total: stats[0]?.totalBlocked || 0,
          byReason: reasonStats
        }
      }
    });
  } catch (error) {
    console.error('Error fetching blocked users:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch blocked users',
      error: error.message
    });
  }
};

// Get blocked user by ID
const getBlockedUserById = async (req, res) => {
  try {
    const { blockedUserId } = req.params;

    const blockedUser = await BlockedUser.findById(blockedUserId)
      .populate('blocker', 'userName firstName lastName email profile phoneNumber')
      .populate('blocked', 'userName firstName lastName email profile phoneNumber');

    if (!blockedUser) {
      return res.status(404).json({
        success: false,
        message: 'Blocked user record not found'
      });
    }

    res.json({
      success: true,
      data: blockedUser
    });
  } catch (error) {
    console.error('Error fetching blocked user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch blocked user',
      error: error.message
    });
  }
};

// Update blocked user record
const updateBlockedUser = async (req, res) => {
  try {
    const { blockedUserId } = req.params;
    const updateData = req.body;

    // Remove fields that shouldn't be updated directly
    delete updateData.blocker;
    delete updateData.blocked;
    delete updateData._id;
    delete updateData.createdAt;
    delete updateData.updatedAt;

    const blockedUser = await BlockedUser.findByIdAndUpdate(
      blockedUserId,
      updateData,
      { new: true, runValidators: true }
    ).populate('blocker', 'userName firstName lastName email')
     .populate('blocked', 'userName firstName lastName email');

    if (!blockedUser) {
      return res.status(404).json({
        success: false,
        message: 'Blocked user record not found'
      });
    }

    res.json({
      success: true,
      message: 'Blocked user record updated successfully',
      data: blockedUser
    });
  } catch (error) {
    console.error('Error updating blocked user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update blocked user record',
      error: error.message
    });
  }
};

// Unblock user (delete blocked user record)
const unblockUser = async (req, res) => {
  try {
    const { blockedUserId } = req.params;

    const blockedUser = await BlockedUser.findByIdAndDelete(blockedUserId);

    if (!blockedUser) {
      return res.status(404).json({
        success: false,
        message: 'Blocked user record not found'
      });
    }

    res.json({
      success: true,
      message: 'User unblocked successfully'
    });
  } catch (error) {
    console.error('Error unblocking user:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to unblock user',
      error: error.message
    });
  }
};

// Bulk actions on blocked users
const bulkActions = async (req, res) => {
  try {
    const { action, blockedUserIds, updateData } = req.body;

    if (!action || !blockedUserIds || !Array.isArray(blockedUserIds)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request data'
      });
    }

    let result;

    switch (action) {
      case 'unblock':
        result = await BlockedUser.deleteMany({ _id: { $in: blockedUserIds } });
        break;
      case 'update':
        if (!updateData) {
          return res.status(400).json({
            success: false,
            message: 'Update data is required for update action'
          });
        }
        result = await BlockedUser.updateMany(
          { _id: { $in: blockedUserIds } },
          { $set: updateData }
        );
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid action'
        });
    }

    res.json({
      success: true,
      message: `Bulk ${action} completed successfully`,
      data: {
        modifiedCount: result.modifiedCount || result.deletedCount,
        matchedCount: result.matchedCount
      }
    });
  } catch (error) {
    console.error('Error performing bulk action:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to perform bulk action',
      error: error.message
    });
  }
};

module.exports = {
  getAllBlockedUsers,
  getBlockedUserById,
  updateBlockedUser,
  unblockUser,
  bulkActions
};
