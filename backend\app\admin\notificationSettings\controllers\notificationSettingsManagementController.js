const NotificationSettings = require('../../../../db/models/notificationSettingsModel');
const User = require('../../../../db/models/userModel');

// Get all notification settings with pagination and filters
const getAllNotificationSettings = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      emailEnabled,
      dailyLimit,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (page - 1) * limit;
    const query = {};

    // Apply filters
    if (emailEnabled !== undefined) query.emailEnabled = emailEnabled === 'true';
    if (dailyLimit !== undefined) query.dailyLimit = parseInt(dailyLimit);

    // Sort configuration
    const sortConfig = {};
    sortConfig[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Build aggregation pipeline for search
    let pipeline = [
      { $match: query }
    ];

    // Add search functionality
    if (search) {
      pipeline.unshift({
        $lookup: {
          from: 'users',
          localField: 'user',
          foreignField: '_id',
          as: 'userData'
        }
      });
      pipeline.push({
        $match: {
          $or: [
            { 'userData.userName': { $regex: search, $options: 'i' } },
            { 'userData.firstName': { $regex: search, $options: 'i' } },
            { 'userData.lastName': { $regex: search, $options: 'i' } },
            { 'userData.email': { $regex: search, $options: 'i' } }
          ]
        }
      });
    }

    // Add sorting, skip, and limit
    pipeline.push(
      { $sort: sortConfig },
      { $skip: skip },
      { $limit: parseInt(limit) }
    );

    // Execute aggregation or regular query
    let notificationSettings;
    if (search) {
      notificationSettings = await NotificationSettings.aggregate(pipeline);
      // Populate the results
      await NotificationSettings.populate(notificationSettings, [
        { path: 'user', select: 'userName firstName lastName email profile' }
      ]);
    } else {
      notificationSettings = await NotificationSettings.find(query)
        .populate('user', 'userName firstName lastName email profile')
        .sort(sortConfig)
        .skip(skip)
        .limit(parseInt(limit));
    }

    const total = await NotificationSettings.countDocuments(query);

    // Get statistics
    const stats = await NotificationSettings.aggregate([
      {
        $group: {
          _id: null,
          totalSettings: { $sum: 1 },
          emailEnabledCount: { $sum: { $cond: ['$emailEnabled', 1, 0] } },
          byDailyLimit: {
            $push: '$dailyLimit'
          },
          quietHoursEnabled: { $sum: { $cond: ['$quietHours.enabled', 1, 0] } }
        }
      }
    ]);

    const dailyLimitStats = {};
    if (stats.length > 0) {
      stats[0].byDailyLimit.forEach(limit => {
        const key = limit === -1 ? 'unlimited' : limit.toString();
        dailyLimitStats[key] = (dailyLimitStats[key] || 0) + 1;
      });
    }

    res.json({
      success: true,
      data: {
        notificationSettings,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / limit),
          totalItems: total,
          hasNextPage: page < Math.ceil(total / limit),
          hasPrevPage: page > 1
        },
        statistics: {
          total: stats[0]?.totalSettings || 0,
          emailEnabled: stats[0]?.emailEnabledCount || 0,
          quietHoursEnabled: stats[0]?.quietHoursEnabled || 0,
          byDailyLimit: dailyLimitStats
        }
      }
    });
  } catch (error) {
    console.error('Error fetching notification settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch notification settings',
      error: error.message
    });
  }
};

// Get notification settings by ID
const getNotificationSettingsById = async (req, res) => {
  try {
    const { settingsId } = req.params;

    const settings = await NotificationSettings.findById(settingsId)
      .populate('user', 'userName firstName lastName email profile phoneNumber');

    if (!settings) {
      return res.status(404).json({
        success: false,
        message: 'Notification settings not found'
      });
    }

    res.json({
      success: true,
      data: settings
    });
  } catch (error) {
    console.error('Error fetching notification settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch notification settings',
      error: error.message
    });
  }
};

// Update notification settings
const updateNotificationSettings = async (req, res) => {
  try {
    const { settingsId } = req.params;
    const updateData = req.body;

    // Remove fields that shouldn't be updated directly
    delete updateData.user;
    delete updateData._id;
    delete updateData.createdAt;
    delete updateData.updatedAt;

    const settings = await NotificationSettings.findByIdAndUpdate(
      settingsId,
      updateData,
      { new: true, runValidators: true }
    ).populate('user', 'userName firstName lastName email');

    if (!settings) {
      return res.status(404).json({
        success: false,
        message: 'Notification settings not found'
      });
    }

    res.json({
      success: true,
      message: 'Notification settings updated successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error updating notification settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update notification settings',
      error: error.message
    });
  }
};

// Delete notification settings
const deleteNotificationSettings = async (req, res) => {
  try {
    const { settingsId } = req.params;

    const settings = await NotificationSettings.findByIdAndDelete(settingsId);

    if (!settings) {
      return res.status(404).json({
        success: false,
        message: 'Notification settings not found'
      });
    }

    res.json({
      success: true,
      message: 'Notification settings deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting notification settings:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete notification settings',
      error: error.message
    });
  }
};

// Bulk actions on notification settings
const bulkActions = async (req, res) => {
  try {
    const { action, settingsIds, updateData } = req.body;

    if (!action || !settingsIds || !Array.isArray(settingsIds)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request data'
      });
    }

    let result;

    switch (action) {
      case 'delete':
        result = await NotificationSettings.deleteMany({ _id: { $in: settingsIds } });
        break;
      case 'enableEmail':
        result = await NotificationSettings.updateMany(
          { _id: { $in: settingsIds } },
          { $set: { emailEnabled: true } }
        );
        break;
      case 'disableEmail':
        result = await NotificationSettings.updateMany(
          { _id: { $in: settingsIds } },
          { $set: { emailEnabled: false } }
        );
        break;
      case 'update':
        if (!updateData) {
          return res.status(400).json({
            success: false,
            message: 'Update data is required for update action'
          });
        }
        result = await NotificationSettings.updateMany(
          { _id: { $in: settingsIds } },
          { $set: updateData }
        );
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid action'
        });
    }

    res.json({
      success: true,
      message: `Bulk ${action} completed successfully`,
      data: {
        modifiedCount: result.modifiedCount || result.deletedCount,
        matchedCount: result.matchedCount
      }
    });
  } catch (error) {
    console.error('Error performing bulk action:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to perform bulk action',
      error: error.message
    });
  }
};

module.exports = {
  getAllNotificationSettings,
  getNotificationSettingsById,
  updateNotificationSettings,
  deleteNotificationSettings,
  bulkActions
};
