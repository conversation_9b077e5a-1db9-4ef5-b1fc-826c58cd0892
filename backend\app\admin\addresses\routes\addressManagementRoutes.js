const express = require('express');
const router = express.Router();
const addressManagementController = require('../controllers/addressManagementController');
const { verifyAdminToken, checkPermission } = require('../../auth/middleware/adminAuthMiddleware');

// All routes require admin authentication
router.use(verifyAdminToken);

// Get all addresses with pagination and filters
router.get('/', 
  checkPermission('addresses', 'read'), 
  addressManagementController.getAllAddresses
);

// Get address by ID
router.get('/:addressId', 
  checkPermission('addresses', 'read'), 
  addressManagementController.getAddressById
);

// Update address
router.patch('/:addressId', 
  checkPermission('addresses', 'update'), 
  addressManagementController.updateAddress
);

// Delete address
router.delete('/:addressId', 
  checkPermission('addresses', 'delete'), 
  addressManagementController.deleteAddress
);

// Bulk actions on addresses
router.post('/bulk-actions', 
  checkPermission('addresses', 'update'), 
  addressManagementController.bulkActions
);

module.exports = router;
