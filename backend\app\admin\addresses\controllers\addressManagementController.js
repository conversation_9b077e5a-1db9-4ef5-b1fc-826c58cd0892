const Address = require('../../../../db/models/addressModel');
const User = require('../../../../db/models/userModel');

// Get all addresses with pagination and filters
const getAllAddresses = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search,
      country,
      addressType,
      isDefault,
      isActive,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (page - 1) * limit;
    const query = {};

    // Build search query
    if (search) {
      query.$or = [
        { fullName: { $regex: search, $options: 'i' } },
        { street1: { $regex: search, $options: 'i' } },
        { city: { $regex: search, $options: 'i' } },
        { zipCode: { $regex: search, $options: 'i' } }
      ];
    }

    // Apply filters
    if (country) query.country = country;
    if (addressType) query.addressType = addressType;
    if (isDefault !== undefined) query.isDefault = isDefault === 'true';
    if (isActive !== undefined) query.isActive = isActive === 'true';

    // Sort configuration
    const sortConfig = {};
    sortConfig[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with population
    const addresses = await Address.find(query)
      .populate('user', 'userName firstName lastName email profile')
      .sort(sortConfig)
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Address.countDocuments(query);

    // Get statistics
    const stats = await Address.aggregate([
      {
        $group: {
          _id: null,
          totalAddresses: { $sum: 1 },
          activeAddresses: { $sum: { $cond: ['$isActive', 1, 0] } },
          defaultAddresses: { $sum: { $cond: ['$isDefault', 1, 0] } },
          byCountry: { $push: '$country' },
          byType: { $push: '$addressType' }
        }
      }
    ]);

    const countryStats = {};
    const typeStats = {};
    
    if (stats.length > 0) {
      stats[0].byCountry.forEach(country => {
        countryStats[country] = (countryStats[country] || 0) + 1;
      });
      
      stats[0].byType.forEach(type => {
        typeStats[type] = (typeStats[type] || 0) + 1;
      });
    }

    res.json({
      success: true,
      data: {
        addresses,
        pagination: {
          currentPage: parseInt(page),
          totalPages: Math.ceil(total / limit),
          totalItems: total,
          hasNextPage: page < Math.ceil(total / limit),
          hasPrevPage: page > 1
        },
        statistics: {
          total: stats[0]?.totalAddresses || 0,
          active: stats[0]?.activeAddresses || 0,
          default: stats[0]?.defaultAddresses || 0,
          byCountry: countryStats,
          byType: typeStats
        }
      }
    });
  } catch (error) {
    console.error('Error fetching addresses:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch addresses',
      error: error.message
    });
  }
};

// Get address by ID
const getAddressById = async (req, res) => {
  try {
    const { addressId } = req.params;

    const address = await Address.findById(addressId)
      .populate('user', 'userName firstName lastName email profile phoneNumber');

    if (!address) {
      return res.status(404).json({
        success: false,
        message: 'Address not found'
      });
    }

    res.json({
      success: true,
      data: address
    });
  } catch (error) {
    console.error('Error fetching address:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch address',
      error: error.message
    });
  }
};

// Update address
const updateAddress = async (req, res) => {
  try {
    const { addressId } = req.params;
    const updateData = req.body;

    // Remove fields that shouldn't be updated directly
    delete updateData.user;
    delete updateData._id;
    delete updateData.createdAt;
    delete updateData.updatedAt;

    const address = await Address.findByIdAndUpdate(
      addressId,
      updateData,
      { new: true, runValidators: true }
    ).populate('user', 'userName firstName lastName email');

    if (!address) {
      return res.status(404).json({
        success: false,
        message: 'Address not found'
      });
    }

    res.json({
      success: true,
      message: 'Address updated successfully',
      data: address
    });
  } catch (error) {
    console.error('Error updating address:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update address',
      error: error.message
    });
  }
};

// Delete address
const deleteAddress = async (req, res) => {
  try {
    const { addressId } = req.params;

    const address = await Address.findByIdAndDelete(addressId);

    if (!address) {
      return res.status(404).json({
        success: false,
        message: 'Address not found'
      });
    }

    res.json({
      success: true,
      message: 'Address deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting address:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete address',
      error: error.message
    });
  }
};

// Bulk actions on addresses
const bulkActions = async (req, res) => {
  try {
    const { action, addressIds, updateData } = req.body;

    if (!action || !addressIds || !Array.isArray(addressIds)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid request data'
      });
    }

    let result;

    switch (action) {
      case 'delete':
        result = await Address.deleteMany({ _id: { $in: addressIds } });
        break;
      case 'activate':
        result = await Address.updateMany(
          { _id: { $in: addressIds } },
          { $set: { isActive: true } }
        );
        break;
      case 'deactivate':
        result = await Address.updateMany(
          { _id: { $in: addressIds } },
          { $set: { isActive: false } }
        );
        break;
      case 'update':
        if (!updateData) {
          return res.status(400).json({
            success: false,
            message: 'Update data is required for update action'
          });
        }
        result = await Address.updateMany(
          { _id: { $in: addressIds } },
          { $set: updateData }
        );
        break;
      default:
        return res.status(400).json({
          success: false,
          message: 'Invalid action'
        });
    }

    res.json({
      success: true,
      message: `Bulk ${action} completed successfully`,
      data: {
        modifiedCount: result.modifiedCount || result.deletedCount,
        matchedCount: result.matchedCount
      }
    });
  } catch (error) {
    console.error('Error performing bulk action:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to perform bulk action',
      error: error.message
    });
  }
};

module.exports = {
  getAllAddresses,
  getAddressById,
  updateAddress,
  deleteAddress,
  bulkActions
};
