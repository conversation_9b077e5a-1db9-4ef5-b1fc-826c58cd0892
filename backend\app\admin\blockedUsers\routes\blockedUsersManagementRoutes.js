const express = require('express');
const router = express.Router();
const blockedUsersManagementController = require('../controllers/blockedUsersManagementController');
const { verifyAdminToken, checkPermission } = require('../../auth/middleware/adminAuthMiddleware');

// All routes require admin authentication
router.use(verifyAdminToken);

// Get all blocked users with pagination and filters
router.get('/', 
  checkPermission('blockedUsers', 'read'), 
  blockedUsersManagementController.getAllBlockedUsers
);

// Get blocked user by ID
router.get('/:blockedUserId', 
  checkPermission('blockedUsers', 'read'), 
  blockedUsersManagementController.getBlockedUserById
);

// Update blocked user record
router.patch('/:blockedUserId', 
  checkPermission('blockedUsers', 'update'), 
  blockedUsersManagementController.updateBlockedUser
);

// Unblock user (delete blocked user record)
router.delete('/:blockedUserId', 
  checkPermission('blockedUsers', 'delete'), 
  blockedUsersManagementController.unblockUser
);

// Bulk actions on blocked users
router.post('/bulk-actions', 
  checkPermission('blockedUsers', 'update'), 
  blockedUsersManagementController.bulkActions
);

module.exports = router;
